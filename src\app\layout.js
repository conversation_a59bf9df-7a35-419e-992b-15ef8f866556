import { Geist, <PERSON>ei<PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "BoostFunda - Helping Grow Your Business and Social Media",
  description: "BoostFunda is the most TRUSTED and SECURE platform to improve your online business reputation and boost your TrustScore. We help you improve your online presence and ratings on Google, Trustpilot, and more.",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
